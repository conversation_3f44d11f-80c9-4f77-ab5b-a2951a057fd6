{"runtimeOptions": {"tfm": "net8.0", "frameworks": [{"name": "Microsoft.NETCore.App", "version": "8.0.0"}, {"name": "Microsoft.WindowsDesktop.App", "version": "8.0.0"}], "additionalProbingPaths": ["C:\\Users\\<USER>\\.dotnet\\store\\|arch|\\|tfm|", "C:\\Users\\<USER>\\.nuget\\packages", "d:\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configProperties": {"System.Runtime.Serialization.EnableUnsafeBinaryFormatterSerialization": true, "Microsoft.NETCore.DotNetHostPolicy.SetAppPaths": true}}}