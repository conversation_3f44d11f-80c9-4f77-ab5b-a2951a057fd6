﻿"restore":{"projectUniqueName":"D:\\netProject\\wpfprinterapp\\wpfprinterapp\\wpfprinterapp.csproj","projectName":"wpfprinterapp","projectPath":"D:\\netProject\\wpfprinterapp\\wpfprinterapp\\wpfprinterapp.csproj","outputPath":"D:\\netProject\\wpfprinterapp\\wpfprinterapp\\obj\\","projectStyle":"PackageReference","fallbackFolders":["d:\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net8.0-windows"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"C:\\Program Files\\dotnet\\library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net8.0-windows7.0":{"targetAlias":"net8.0-windows","projectReferences":{}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"}}"frameworks":{"net8.0-windows7.0":{"targetAlias":"net8.0-windows","dependencies":{"QRCoder":{"target":"Package","version":"[1.6.0, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"},"Microsoft.WindowsDesktop.App.WPF":{"privateAssets":"none"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\8.0.400/PortableRuntimeIdentifierGraph.json"}}