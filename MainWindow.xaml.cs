﻿using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace wpfprinterapp
{
    /// <summary>
    /// 智能标签打印系统主窗口
    /// </summary>
    public partial class MainWindow : Window
    {
        // 产品数据映射
        private readonly Dictionary<string, ProductInfo> _productMap = new()
        {
            { "e06 - 女式牛仔裤", new ProductInfo("e06", "女式牛仔裤", "件") },
            { "e10 - 男士T恤", new ProductInfo("e10", "男士T恤", "件") },
            { "e12 - 运动鞋", new ProductInfo("e12", "运动鞋", "双") },
            { "e15 - 西装套装", new ProductInfo("e15", "西装套装", "套") }
        };

        // 尺码数据
        private readonly Dictionary<string, string[]> _sizeOptions = new()
        {
            { "char", new[] { "S", "M", "L", "XL", "XXL", "XXXL" } },
            { "num", new[] { "110", "120", "130", "140", "150", "160", "170", "180" } }
        };

        // 颜色选项
        private readonly string[] _colorOptions = { "黑色", "白色", "牛仔蓝", "卡其色", "红色", "绿色", "灰色" };

        private string _lastValidProduct = "e06 - 女式牛仔裤";

        public MainWindow()
        {
            InitializeComponent();
            InitializeData();
            BindEvents();
            UpdatePreview();
        }

        /// <summary>
        /// 绑定事件处理器
        /// </summary>
        private void BindEvents()
        {
            // 绑定事件处理器
            ProductCodeComboBox.SelectionChanged += ProductCodeComboBox_SelectionChanged;
            ColorComboBox.SelectionChanged += ColorComboBox_SelectionChanged;
            SizeComboBox.SelectionChanged += SizeComboBox_SelectionChanged;
            QuantityTextBox.TextChanged += QuantityTextBox_TextChanged;
            CharSizeRadio.Checked += SizeTypeRadio_Checked;
            NumSizeRadio.Checked += SizeTypeRadio_Checked;
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            // 初始化产品编号下拉框
            foreach (var product in _productMap.Keys)
            {
                ProductCodeComboBox.Items.Add(product);
            }
            ProductCodeComboBox.SelectedItem = _lastValidProduct;

            // 初始化颜色下拉框
            foreach (var color in _colorOptions)
            {
                ColorComboBox.Items.Add(color);
            }
            ColorComboBox.SelectedItem = "牛仔蓝";

            // 初始化尺码
            UpdateSizeOptions();
        }

        /// <summary>
        /// 更新尺码选项
        /// </summary>
        private void UpdateSizeOptions()
        {
            // 检查控件是否已初始化
            if (SizeComboBox == null || CharSizeRadio == null)
                return;

            var sizeType = CharSizeRadio.IsChecked == true ? "char" : "num";
            var currentSize = SizeComboBox.SelectedItem?.ToString();

            SizeComboBox.Items.Clear();
            foreach (var size in _sizeOptions[sizeType])
            {
                SizeComboBox.Items.Add(size);
            }

            // 保持当前选择或选择第一个
            if (!string.IsNullOrEmpty(currentSize) && _sizeOptions[sizeType].Contains(currentSize))
            {
                SizeComboBox.SelectedItem = currentSize;
            }
            else
            {
                SizeComboBox.SelectedIndex = 0;
            }

            UpdatePreview();
        }

        /// <summary>
        /// 更新标签预览
        /// </summary>
        private void UpdatePreview()
        {
            try
            {
                // 检查所有必要的控件是否已初始化
                if (ProductCodeComboBox == null || ColorComboBox == null ||
                    SizeComboBox == null || QuantityTextBox == null ||
                    UnitTextBox == null || LabelSkuTextBlock == null ||
                    LabelQuantityTextBlock == null || QrDataTextBlock == null)
                {
                    return;
                }

                var productDisplayValue = ProductCodeComboBox.Text ?? "";
                if (string.IsNullOrEmpty(productDisplayValue) || !_productMap.ContainsKey(productDisplayValue))
                {
                    return; // 无效产品时不更新预览
                }

                var productInfo = _productMap[productDisplayValue];
                var color = ColorComboBox.Text ?? "";
                var size = SizeComboBox.SelectedItem?.ToString() ?? "";
                var quantity = QuantityTextBox.Text ?? "0";

                // 更新单位
                UnitTextBox.Text = productInfo.Unit;

                // 更新SKU显示
                LabelSkuTextBlock.Text = $"{productInfo.Code}-{productInfo.Name}-{color}-{size}";

                // 更新数量显示
                LabelQuantityTextBlock.Text = $"{quantity} {productInfo.Unit}";

                // 更新二维码数据
                var qrData = $"{productInfo.Code}-{color.ToLower().Replace(" ", "")}-{size}-{quantity}-{productInfo.Unit.ToLower()}";
                QrDataTextBlock.Text = qrData;
            }
            catch (Exception ex)
            {
                // 处理异常，可以记录日志或显示错误信息
                System.Diagnostics.Debug.WriteLine($"更新预览时发生错误: {ex.Message}");
            }
        }

        // 事件处理器
        private void ProductCodeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (ProductCodeComboBox.SelectedItem != null)
            {
                _lastValidProduct = ProductCodeComboBox.SelectedItem.ToString()!;
                UpdatePreview();
            }
        }

        private void ColorComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void SizeTypeRadio_Checked(object sender, RoutedEventArgs e)
        {
            UpdateSizeOptions();
        }

        private void SizeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void QuantityTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            UpdatePreview();
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var printCount = int.Parse(PrintCountTextBox.Text);
                var productName = ProductCodeComboBox.Text;
                var color = ColorComboBox.Text;
                var size = SizeComboBox.SelectedItem?.ToString();
                var quantity = QuantityTextBox.Text;
                var unit = UnitTextBox.Text;

                var message = $"准备打印 {printCount} 张标签:\n" +
                             $"产品: {productName}\n" +
                             $"颜色: {color}\n" +
                             $"尺码: {size}\n" +
                             $"数量: {quantity} {unit}";

                MessageBox.Show(message, "打印确认", MessageBoxButton.OK, MessageBoxImage.Information);

                // TODO: 在这里实现实际的打印逻辑
                // 可以调用打印机API或生成打印文件
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打印时发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    /// <summary>
    /// 产品信息类
    /// </summary>
    public class ProductInfo
    {
        public string Code { get; }
        public string Name { get; }
        public string Unit { get; }

        public ProductInfo(string code, string name, string unit)
        {
            Code = code;
            Name = name;
            Unit = unit;
        }
    }
}