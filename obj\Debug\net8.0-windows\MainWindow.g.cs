﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "5E978FCCB868F04F92F0167EF96E934D1BFD15C8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using wpfprinterapp;


namespace wpfprinterapp {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 184 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ProductCodeComboBox;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ColorComboBox;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton CharSizeRadio;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton NumSizeRadio;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SizeComboBox;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox UnitTextBox;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox QuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PrintCountTextBox;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LabelSkuTextBlock;
        
        #line default
        #line hidden
        
        
        #line 306 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LabelQuantityTextBlock;
        
        #line default
        #line hidden
        
        
        #line 332 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock QrDataTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.8.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/wpfprinterapp;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.8.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ProductCodeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 188 "..\..\..\MainWindow.xaml"
            this.ProductCodeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ProductCodeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ColorComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 196 "..\..\..\MainWindow.xaml"
            this.ColorComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ColorComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CharSizeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 205 "..\..\..\MainWindow.xaml"
            this.CharSizeRadio.Checked += new System.Windows.RoutedEventHandler(this.SizeTypeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 4:
            this.NumSizeRadio = ((System.Windows.Controls.RadioButton)(target));
            
            #line 209 "..\..\..\MainWindow.xaml"
            this.NumSizeRadio.Checked += new System.Windows.RoutedEventHandler(this.SizeTypeRadio_Checked);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SizeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 217 "..\..\..\MainWindow.xaml"
            this.SizeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SizeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.UnitTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.QuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 239 "..\..\..\MainWindow.xaml"
            this.QuantityTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.QuantityTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.PrintCountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            
            #line 253 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PrintButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.LabelSkuTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.LabelQuantityTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.QrDataTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

